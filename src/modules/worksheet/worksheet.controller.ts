import { Controller, Post, Body, Get, Param, Query, Delete, HttpCode, HttpStatus, ForbiddenException, NotFoundException, UseGuards, Logger, Patch, Put, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Roles } from '../auth/decorators/role.decorator';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { EUserRole } from '../user/dto/create-user.dto';
import { User } from '../user/entities/user.entity';
import { WorksheetService } from './worksheet.service';
import { CreateWorksheetDto } from './dto/create-worksheet.dto';
import { ListWorksheetDto } from './dto/list-worksheets.dto';
import { WorksheetCleanupService } from './worksheet-cleanup.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetDocument } from '../mongodb/schemas/worksheet-document.schema';
import { WorksheetQuestionService } from './services/worksheet-question.service';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto, ReplaceWorksheetQuestionDto, BulkReorderQuestionsDto } from './dto/worksheet-question.dto';
import { BulkAddQuestionsDto, BulkRemoveQuestionsDto, BulkUpdateQuestionsDto, BulkOperationResponseDto } from './dto/bulk-operations.dto';
import { WorksheetQuestionThrottlerGuard } from './guards/worksheet-question-throttler.guard';
import { WorksheetQuestionMetricsInterceptor } from './interceptors/worksheet-question-metrics.interceptor';
import { UserContext } from '../auth/services/rbac.service';

@ApiTags('Worksheet')
@Controller('worksheets')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetController {
  private readonly logger = new Logger(WorksheetController.name);

  constructor(
    private readonly worksheetService: WorksheetService,
    private readonly worksheetCleanupService: WorksheetCleanupService,
    private readonly worksheetQuestionService: WorksheetQuestionService,
    @InjectModel(WorksheetDocument.name)
    private worksheetDocumentModel: Model<WorksheetDocument>,
  ) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Create a new worksheet' })
  @ApiBody({ type: CreateWorksheetDto })
  @ApiResponse({ status: 201, description: 'Worksheet created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createWorksheetDto: CreateWorksheetDto, @ActiveUser() user: User) {
    return this.worksheetService.create(createWorksheetDto, user);
  }

  @Get('cache/metrics')
  @Public()
  @ApiOperation({ summary: 'Get document cache metrics' })
  @ApiResponse({ status: 200, description: 'Cache metrics retrieved successfully' })
  async getCacheMetrics() {
    try {
      // Get total document count
      const totalDocuments = await this.worksheetDocumentModel.countDocuments();

      // Get count of documents from cache
      const cachedDocuments = await this.worksheetDocumentModel.countDocuments({ fromCache: true });

      // Get count by hit count ranges
      const hitCountRanges = await Promise.all([
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 10 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 5, $lt: 10 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 2, $lt: 5 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: 1 }),
      ]);

      // Get expiring soon count
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const expiringSoon = await this.worksheetDocumentModel.countDocuments({
        expiresAt: { $lt: tomorrow }
      });

      // Get top 5 topics
      const topTopics = await this.worksheetDocumentModel.aggregate([
        { $group: { _id: "$topic", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]);

      // Calculate cache hit rate
      const cacheHitRate = totalDocuments > 0 ? (cachedDocuments / totalDocuments) * 100 : 0;

      return {
        totalDocuments,
        cachedDocuments,
        cacheHitRate: `${cacheHitRate.toFixed(2)}%`,
        hitCountDistribution: {
          '10+': hitCountRanges[0],
          '5-9': hitCountRanges[1],
          '2-4': hitCountRanges[2],
          '1': hitCountRanges[3],
        },
        expiringSoon,
        topTopics: topTopics.map(t => ({ topic: t._id, count: t.count })),
        lastCacheWarming: await this.getLastCacheWarmingTime(),
      };
    } catch (error) {
      return {
        error: error.message,
        status: 'Error retrieving cache metrics'
      };
    }
  }

  @Get('cache/warm')
  @Public()
  @ApiOperation({ summary: 'Manually trigger cache warming' })
  @ApiResponse({ status: 200, description: 'Cache warming triggered successfully' })
  async triggerCacheWarming() {
    try {
      // Trigger the cache warming job
      await this.worksheetCleanupService.warmCache();

      return {
        status: 'Cache warming triggered successfully',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        error: error.message,
        status: 'Error triggering cache warming'
      };
    }
  }

  @Get()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Get all worksheets with pagination and school-based filtering',
    description: `Retrieves worksheets with pagination support and school-based filtering.

    **School-Based Filtering Behavior:**
    - **Admin users**: Can optionally specify a schoolId to filter worksheets from a specific school. If no schoolId is provided, all worksheets are returned.
    - **Non-admin users** (SCHOOL_MANAGER, TEACHER, INDEPENDENT_TEACHER): Can only access worksheets from their own school. If a schoolId is provided, it must match their own school ID, otherwise a 403 Forbidden error is returned.
    - **Users without school association**: Cannot access any worksheets and will receive empty results.

    **Parameters:**
    - page: Page number for pagination (starts at 1)
    - pageSize: Number of items per page
    - schoolId: Optional UUID to filter worksheets by school (admin users only for cross-school access)`
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Worksheets retrieved successfully',
    schema: {
      properties: {
        items: {
          type: 'array',
          description: 'Array of worksheet items for the current page',
          items: {
            type: 'object',
            // Worksheet properties would be defined here
          }
        },
        meta: {
          type: 'object',
          description: 'Pagination metadata',
          properties: {
            page: {
              type: 'number',
              description: 'Current page number',
              example: 1
            },
            pageSize: {
              type: 'number',
              description: 'Number of items per page',
              example: 10
            },
            total: {
              type: 'number',
              description: 'Total number of items across all pages',
              example: 100
            },
            totalPages: {
              type: 'number',
              description: 'Total number of pages',
              example: 10
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid query parameters (e.g., invalid UUID format for schoolId)'
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Missing or invalid authentication token'
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User attempted to access worksheets from a different school or user is not associated with any school'
  })
  async findAll(
    @Query() listWorksheetDto: ListWorksheetDto,
    @ActiveUser() user: User,
  ) {
    return this.worksheetService.findAll(listWorksheetDto, user);
  }

  @Patch('/questions/reorder/:id')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Reorder questions in a worksheet',})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({
    type: BulkReorderQuestionsDto,
    description: 'Array of question reorder operations',
    examples: {
      singleReorder: {
        summary: 'Single question reorder',
        value: {
          reorders: [
            {
              questionId: 'question-123',
              newPosition: 3
            }
          ]
        }
      },
      bulkReorder: {
        summary: 'Multiple questions reorder',
        value: {
          reorders: [
            {
              questionId: 'question-123',
              newPosition: 1
            },
            {
              questionId: 'question-456',
              newPosition: 2
            },
            {
              questionId: 'question-789',
              newPosition: 3
            }
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Questions reordered successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Questions reordered successfully' },
        data: {
          type: 'object',
          properties: {
            worksheetId: { type: 'string', example: 'worksheet-123' },
            totalQuestions: { type: 'number', example: 10 },
            reorderedQuestions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  questionId: { type: 'string', example: 'question-123' },
                  oldPosition: { type: 'number', example: 5 },
                  newPosition: { type: 'number', example: 3 }
                }
              }
            },
            version: { type: 'number', example: 15 }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question IDs, positions, or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found or question IDs not found in worksheet' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected (optimistic locking)' })
  async reorderQuestions(
    @Param('id') worksheetId: string,
    @Body() reorderDto: BulkReorderQuestionsDto,
    @ActiveUser() user: UserContext
  ) {
    try {
      const result = await this.worksheetQuestionService.reorderQuestionsInWorksheet(
        worksheetId,
        reorderDto,
        {
          sub: user.sub,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: result,
        message: 'Questions reordered successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to reorder questions in worksheet ${worksheetId}`, error);
      throw error;
    }
  }

  /**
   * Replace a question with a similar question from the question pool
   */
  @Post(':id/questions/replace-with-similar/:questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Replace a question with a similar question from the pool',
    description: 'Remove the specified question and replace it with a similar question from the question pool based on the same criteria (subject, grade, difficulty, type)'
  })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiParam({ name: 'questionId', description: 'ID of the question to be replaced' })
  @ApiResponse({
    status: 200,
    description: 'Question replaced successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Question replaced with similar question successfully' },
        data: {
          type: 'object',
          properties: {
            worksheetId: { type: 'string' },
            oldQuestionId: { type: 'string' },
            newQuestion: { type: 'object', description: 'The new question object' },
            position: { type: 'number', description: 'Position where the question was replaced' },
            totalQuestions: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question ID or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found, or no similar questions available' })
  async replaceQuestionWithSimilar(
    @Param('id') worksheetId: string,
    @Param('questionId') questionId: string,
    @ActiveUser() user: User
  ) {
    try {
      this.logger.log(`Replacing question ${questionId} with similar question in worksheet ${worksheetId} by user ${user.id}`);

      const result = await this.worksheetQuestionService.replaceQuestionWithSimilar(
        worksheetId,
        questionId,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        }
      );

      return {
        success: true,
        message: 'Question replaced with similar question successfully',
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to replace question ${questionId} with similar question in worksheet ${worksheetId}`, error);
      throw error;
    }
  }

  @Delete(':id/questions/:questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({summary: 'Remove a question from a worksheet'})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiParam({ name: 'questionId', description: 'Question ID to remove' })
  @ApiResponse({ status: 204, description: 'Question removed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot remove last question or invalid parameters' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async removeQuestion(
    @Param('id') worksheetId: string,
    @Param('questionId') questionId: string,
    @ActiveUser() user: UserContext
  ): Promise<void> {
    try {
      await this.worksheetQuestionService.removeQuestionFromWorksheet(
        worksheetId,
        questionId,
        {
          sub: user.sub,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );
    } catch (error) {
      this.logger.error(`Failed to remove question ${questionId} from worksheet ${worksheetId}`, error);

      // Re-throw the error to let NestJS handle the HTTP response
      throw error;
    }
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Get worksheet by ID with school-based access control'})
  @ApiParam({ name: 'id', description: 'Worksheet ID (UUID format)' })
  @ApiResponse({ status: 200, description: 'Worksheet retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Missing or invalid authentication token' })
  @ApiResponse({ status: 404, description: 'Worksheet not found or user does not have access to this worksheet' })
  findOne(@Param('id') id: string, @ActiveUser() user: User) {
    return this.worksheetService.findOne(id, user);
  }

  @Delete(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a worksheet' })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiResponse({ status: 204, description: 'Worksheet deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden. User does not have permission to delete this worksheet.' })
  @ApiResponse({ status: 404, description: 'Worksheet not found.' })
  async remove(@Param('id') id: string, @ActiveUser() user: User): Promise<void> {
    // The service handles both finding the worksheet and authorization checks
    // including admin access to all schools
    return this.worksheetService.remove(id, user);
  }

  @Post(':id/questions')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Add a question to a worksheet'})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({ type: AddQuestionToWorksheetDto })
  @ApiResponse({
    status: 201,
    description: 'Question added successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The created question object'
        },
        message: { type: 'string', example: 'Question added successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or limit exceeded' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async addQuestion(
    @Param('id') worksheetId: string,
    @Body() questionDto: AddQuestionToWorksheetDto,
    @ActiveUser() user: User
  ) {
    try {
      const question = await this.worksheetQuestionService.addQuestionToWorksheet(
        worksheetId,
        questionDto,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: question,
        message: 'Question added successfully'
      };
    } catch (error) {
      // Log the error for debugging
      console.error(`Error adding question to worksheet ${worksheetId}:`, error);
      throw error; // Re-throw to let NestJS handle the HTTP response
    }
  }

  @Patch(':id/questions/:questionId')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Update a question in a worksheet (partial update)'})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiParam({ name: 'questionId', description: 'Question ID to update' })
  @ApiBody({ type: UpdateWorksheetQuestionDto })
  @ApiResponse({
    status: 200,
    description: 'Question updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The updated question object'
        },
        message: { type: 'string', example: 'Question updated successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or validation errors' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected (version mismatch)' })
  async updateQuestion(
    @Param('id') worksheetId: string,
    @Param('questionId') questionId: string,
    @Body() updateDto: UpdateWorksheetQuestionDto,
    @ActiveUser() user: User
  ) {
    try {
      const updatedQuestion = await this.worksheetQuestionService.updateQuestionInWorksheet(
        worksheetId,
        questionId,
        updateDto,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: updatedQuestion,
        message: 'Question updated successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to update question ${questionId} in worksheet ${worksheetId}`, error);
      throw error;
    }
  }

  /**
   * Bulk add questions to a worksheet
   */
  @Post(':id/questions/bulk-add')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Bulk add questions to a worksheet'})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({ type: BulkAddQuestionsDto })
  @ApiResponse({
    status: 201,
    description: 'Bulk add operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data or question limit exceeded' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async bulkAddQuestions(
    @Param('id') worksheetId: string,
    @Body() bulkAddDto: BulkAddQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const result = await this.worksheetQuestionService.bulkAddQuestions(
        worksheetId,
        bulkAddDto.questions,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        {
          insertPosition: bulkAddDto.insertPosition,
          validateQuestions: bulkAddDto.validateQuestions,
          reason: bulkAddDto.reason
        }
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk remove questions from a worksheet
   */
  @Delete(':id/questions/bulk-remove')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Bulk remove questions from a worksheet'})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({ type: BulkRemoveQuestionsDto })

  @ApiResponse({
    status: 200,
    description: 'Bulk remove operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data or minimum question violation' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async bulkRemoveQuestions(
    @Param('id') worksheetId: string,
    @Body() bulkRemoveDto: BulkRemoveQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const result = await this.worksheetQuestionService.bulkRemoveQuestions(
        worksheetId,
        bulkRemoveDto.questionIds,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        {
          forceRemoval: bulkRemoveDto.forceRemoval,
          reason: bulkRemoveDto.reason
        }
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk update questions in a worksheet
   */
  @Patch(':id/questions/bulk-update')
  @UseGuards(WorksheetQuestionThrottlerGuard)
  @UseInterceptors(WorksheetQuestionMetricsInterceptor)
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({summary: 'Bulk update questions in a worksheet',})
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({ type: BulkUpdateQuestionsDto })
  @ApiResponse({
    status: 200,
    description: 'Bulk update operation completed',
    type: BulkOperationResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Version mismatch (optimistic locking)' })
  async bulkUpdateQuestions(
    @Param('id') worksheetId: string,
    @Body() bulkUpdateDto: BulkUpdateQuestionsDto,
    @ActiveUser() user: User
  ): Promise<BulkOperationResponseDto> {
    const startTime = Date.now();

    try {
      const result = await this.worksheetQuestionService.bulkUpdateQuestions(
        worksheetId,
        bulkUpdateDto.updates,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        {
          validateQuestions: bulkUpdateDto.validateQuestions,
          reason: bulkUpdateDto.reason
        }
      );

      return {
        success: result.success,
        successCount: result.successCount,
        failureCount: result.failureCount,
        totalCount: result.totalCount,
        successes: result.successes,
        failures: result.failures,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - startTime
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Helper method to get the last cache warming time
   */
  private async getLastCacheWarmingTime(): Promise<string> {
    try {
      // Find the most recent cache-warming document
      const latestWarming = await this.worksheetDocumentModel
        .findOne({ worksheetId: { $regex: /^cache-warming-/ } })
        .sort({ createdAt: -1 });

      return latestWarming ? latestWarming.createdAt.toISOString() : 'Never';
    } catch (error) {
      return 'Unknown';
    }
  }

  

  /**
   * Sync MongoDB with PostgreSQL for a specific worksheet
   */
  @Post(':id/sync')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Sync MongoDB with PostgreSQL for a worksheet',
    description: 'Fix data inconsistencies between PostgreSQL and MongoDB for a specific worksheet. Admin/School Manager only.'
  })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiQuery({
    name: 'dryRun',
    description: 'Whether to perform a dry run (show changes without applying them)',
    required: false,
    type: Boolean,
    example: false
  })
  @ApiResponse({
    status: 200,
    description: 'Sync operation completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        summary: { type: 'string', example: 'Successfully made 2 changes: 1 removals, 1 additions, 0 reorderings' },
        changes: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              action: { type: 'string', enum: ['remove', 'add', 'reorder'] },
              questionId: { type: 'string' },
              details: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager access required' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async syncWorksheet(
    @Param('id') worksheetId: string,
    @Query('dryRun') dryRun: boolean = false,
    @ActiveUser() user: User
  ) {
    try {
      this.logger.log(`${dryRun ? 'DRY RUN - ' : ''}Syncing worksheet ${worksheetId} by user ${user.id}`);

      const result = await this.worksheetQuestionService.syncMongoDBWithPostgreSQL(
        worksheetId,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId
        },
        { dryRun }
      );

      return {
        success: result.success,
        summary: result.summary,
        changes: result.changes,
        dryRun
      };
    } catch (error) {
      this.logger.error(`Failed to sync worksheet ${worksheetId}`, error);
      throw error;
    }
  }
}
