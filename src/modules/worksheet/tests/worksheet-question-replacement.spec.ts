import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { UserQuestionHistoryService } from '../../question-pool/services/user-question-history.service';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { EUserRole } from '../../user/dto/create-user.dto';

describe('WorksheetQuestionService - Question Replacement', () => {
  let service: WorksheetQuestionService;
  let userQuestionHistoryService: UserQuestionHistoryService;
  let questionPoolService: QuestionPoolService;
  let mockWorksheetRepository: any;
  let mockWorksheetQuestionModel: any;
  let mockWorksheetPromptResultModel: any;

  const mockUser = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockWorksheet = {
    id: 'worksheet-123',
    questionIds: ['question-1', 'question-2'],
    selectedOptions: [
      {
        optionType: { key: 'subject' },
        optionValue: { value: 'Mathematics' },
        text: 'Mathematics'
      },
      {
        optionType: { key: 'grade' },
        optionValue: { value: 'Primary 5' },
        text: 'Primary 5'
      }
    ],
    totalQuestions: 2,
    lastModifiedBy: 'user-123'
  };

  const mockRemovedQuestion = {
    id: 'question-1',
    type: 'multiple_choice',
    content: 'What is 2 + 2?',
    subject: 'Mathematics',
    grade: 'Primary 5',
    difficulty: 'Easy'
  };

  const mockReplacementQuestion = {
    _id: 'pool-question-123',
    type: 'multiple_choice',
    content: 'What is 3 + 3?',
    options: ['5', '6', '7', '8'],
    answer: ['6'],
    explain: '3 + 3 equals 6',
    subject: 'Mathematics',
    grade: 'Primary 5',
    difficulty: 'Easy'
  };

  beforeEach(async () => {
    const mockRepositories = {
      [getRepositoryToken(Worksheet)]: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
      [getModelToken(WorksheetQuestionDocument.name)]: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
      [getModelToken(WorksheetPromptResult.name)]: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: UserQuestionHistoryService,
          useValue: {
            getQuestionIdsToExclude: jest.fn(),
          },
        },
        {
          provide: QuestionPoolService,
          useValue: {
            getRandomQuestionsWithDistribution: jest.fn(),
          },
        },
        // Mock all other dependencies
        {
          provide: 'WorksheetQuestionAuditService',
          useValue: {
            logQuestionRemoved: jest.fn(),
          },
        },
        {
          provide: 'SocketGateway',
          useValue: {
            emitToRoom: jest.fn(),
          },
        },
        {
          provide: 'WorksheetQuestionCollaborationGateway',
          useValue: {
            emitCollaborationEvent: jest.fn(),
          },
        },
        {
          provide: 'WorksheetQuestionLockingService',
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: 'WorksheetQuestionMetricsService',
          useValue: {
            recordMetric: jest.fn(),
          },
        },
        {
          provide: 'WorksheetQuestionEnhancedCacheService',
          useValue: {
            invalidateWorksheetCache: jest.fn(),
          },
        },
        ...Object.entries(mockRepositories).map(([token, mock]) => ({
          provide: token,
          useValue: mock,
        })),
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    userQuestionHistoryService = module.get<UserQuestionHistoryService>(UserQuestionHistoryService);
    questionPoolService = module.get<QuestionPoolService>(QuestionPoolService);
    mockWorksheetRepository = module.get(getRepositoryToken(Worksheet));
    mockWorksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));
    mockWorksheetPromptResultModel = module.get(getModelToken(WorksheetPromptResult.name));
  });

  describe('findAndAddReplacementQuestion', () => {
    it('should find and add a replacement question when one is available', async () => {
      // Setup mocks
      jest.spyOn(userQuestionHistoryService, 'getQuestionIdsToExclude')
        .mockResolvedValue(['excluded-question-1', 'excluded-question-2']);
      
      jest.spyOn(questionPoolService, 'getRandomQuestionsWithDistribution')
        .mockResolvedValue({
          questions: [mockReplacementQuestion],
          metadata: { totalReturned: 1 }
        });

      // Mock addQuestionToWorksheet method
      jest.spyOn(service, 'addQuestionToWorksheet')
        .mockResolvedValue(mockReplacementQuestion as any);

      // Call the private method through reflection
      const findAndAddReplacementQuestion = (service as any).findAndAddReplacementQuestion;
      
      await expect(
        findAndAddReplacementQuestion.call(service, mockWorksheet, mockRemovedQuestion, mockUser)
      ).resolves.not.toThrow();

      // Verify the question pool service was called with correct parameters
      expect(questionPoolService.getRandomQuestionsWithDistribution).toHaveBeenCalledWith({
        subject: 'Mathematics',
        parentSubject: undefined,
        childSubject: undefined,
        type: 'multiple_choice',
        grade: 'Primary 5',
        status: 'active',
        count: 1,
        skipDistribution: true,
        skipDiversity: true,
        skipValidation: true,
        excludeQuestionIds: ['excluded-question-1', 'excluded-question-2']
      });

      // Verify addQuestionToWorksheet was called
      expect(service.addQuestionToWorksheet).toHaveBeenCalledWith(
        mockWorksheet.id,
        expect.objectContaining({
          questionPoolId: 'pool-question-123',
          type: 'multiple_choice',
          content: 'What is 3 + 3?',
          subject: 'Mathematics',
          grade: 'Primary 5'
        }),
        mockUser
      );
    });

    it('should handle case when no replacement question is found', async () => {
      // Setup mocks
      jest.spyOn(userQuestionHistoryService, 'getQuestionIdsToExclude')
        .mockResolvedValue([]);
      
      jest.spyOn(questionPoolService, 'getRandomQuestionsWithDistribution')
        .mockResolvedValue({
          questions: [],
          metadata: { totalReturned: 0 }
        });

      // Call the private method through reflection
      const findAndAddReplacementQuestion = (service as any).findAndAddReplacementQuestion;
      
      // Should not throw, just log a warning
      await expect(
        findAndAddReplacementQuestion.call(service, mockWorksheet, mockRemovedQuestion, mockUser)
      ).resolves.not.toThrow();

      // Verify addQuestionToWorksheet was not called
      expect(service.addQuestionToWorksheet).not.toHaveBeenCalled();
    });
  });

  describe('extractQuestionCriteria', () => {
    it('should extract criteria from removed question and worksheet options', () => {
      const extractQuestionCriteria = (service as any).extractQuestionCriteria;
      
      const criteria = extractQuestionCriteria.call(service, mockRemovedQuestion, mockWorksheet);

      expect(criteria).toEqual({
        subject: 'Mathematics',
        type: 'multiple_choice',
        grade: 'Primary 5',
        difficulty: 'Easy'
      });
    });

    it('should use worksheet options when question criteria is missing', () => {
      const incompleteQuestion = {
        id: 'question-1',
        type: 'multiple_choice',
        content: 'What is 2 + 2?'
        // Missing subject, grade, etc.
      };

      const extractQuestionCriteria = (service as any).extractQuestionCriteria;
      
      const criteria = extractQuestionCriteria.call(service, incompleteQuestion, mockWorksheet);

      expect(criteria).toEqual({
        type: 'multiple_choice',
        subject: 'Mathematics',
        grade: 'Primary 5'
      });
    });
  });

  describe('bulkAddReplacementQuestions', () => {
    it('should find and add multiple replacement questions for bulk removal', async () => {
      // Setup mocks
      jest.spyOn(userQuestionHistoryService, 'getQuestionIdsToExclude')
        .mockResolvedValue(['excluded-question-1']);

      jest.spyOn(questionPoolService, 'getRandomQuestions')
        .mockResolvedValue([
          { ...mockReplacementQuestion, _id: 'pool-question-1' },
          { ...mockReplacementQuestion, _id: 'pool-question-2' },
          { ...mockReplacementQuestion, _id: 'pool-question-3' }
        ]);

      // Mock addQuestionToWorksheet method
      jest.spyOn(service, 'addQuestionToWorksheet')
        .mockResolvedValue(mockReplacementQuestion as any);

      // Call the private method through reflection
      const bulkAddReplacementQuestions = (service as any).bulkAddReplacementQuestions;

      await expect(
        bulkAddReplacementQuestions.call(service, mockWorksheet, 2, mockUser)
      ).resolves.not.toThrow();

      // Verify the question pool service was called
      expect(questionPoolService.getRandomQuestions).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'Mathematics',
          grade: 'Primary 5',
          status: 'active'
        }),
        6 // 2 * 3 for more options
      );

      // Verify addQuestionToWorksheet was called twice
      expect(service.addQuestionToWorksheet).toHaveBeenCalledTimes(2);
    });

    it('should handle case when no replacement questions are found for bulk removal', async () => {
      // Setup mocks
      jest.spyOn(userQuestionHistoryService, 'getQuestionIdsToExclude')
        .mockResolvedValue([]);

      jest.spyOn(questionPoolService, 'getRandomQuestions')
        .mockResolvedValue([]);

      // Call the private method through reflection
      const bulkAddReplacementQuestions = (service as any).bulkAddReplacementQuestions;

      // Should not throw, just log a warning
      await expect(
        bulkAddReplacementQuestions.call(service, mockWorksheet, 3, mockUser)
      ).resolves.not.toThrow();

      // Verify addQuestionToWorksheet was not called
      expect(service.addQuestionToWorksheet).not.toHaveBeenCalled();
    });
  });

  describe('extractWorksheetCriteria', () => {
    it('should extract criteria from worksheet options', () => {
      const extractWorksheetCriteria = (service as any).extractWorksheetCriteria;

      const criteria = extractWorksheetCriteria.call(service, mockWorksheet);

      expect(criteria).toEqual({
        subject: 'Mathematics',
        grade: 'Primary 5'
      });
    });

    it('should handle worksheet with no options', () => {
      const worksheetWithoutOptions = {
        ...mockWorksheet,
        selectedOptions: []
      };

      const extractWorksheetCriteria = (service as any).extractWorksheetCriteria;

      const criteria = extractWorksheetCriteria.call(service, worksheetWithoutOptions);

      expect(criteria).toEqual({});
    });
  });
});
